package com.ecco.security.web;

import javax.annotation.PostConstruct;

import org.apache.commons.lang.StringUtils;
import org.springframework.core.io.UrlResource;
import org.springframework.ldap.core.support.LdapContextSource;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.kerberos.authentication.KerberosServiceAuthenticationProvider;
import org.springframework.security.kerberos.authentication.KerberosServiceRequestToken;
import org.springframework.security.kerberos.authentication.sun.SunJaasKerberosTicketValidator;
import org.springframework.security.ldap.DefaultSpringSecurityContextSource;
import org.springframework.security.ldap.search.FilterBasedLdapUserSearch;
import org.springframework.security.ldap.search.LdapUserSearch;
import org.springframework.security.ldap.userdetails.LdapUserDetailsService;
import org.springframework.security.ldap.userdetails.UserDetailsContextMapper;

import com.ecco.config.service.SettingsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * A Kerberos authentication provider wrapper
 * which delegates authentication to spring's {@see KerberosServiceAuthenticationProvider}
 * This adds value for the scenario when Kerberos authn is not switched on (hence not configured in ecco)
 * a {@code null} will be returned from {@link KerberosDelegatingAuthenticationProvider#authenticate}
 * which means the next configured AuthenticationProvider will be used by Spring (if available)
 * See http://static.springsource.org/spring-security/site/docs/3.1.x/reference/springsecurity-single.html#core-services-authentication-manager
 */
@RequiredArgsConstructor
@Slf4j
public class KerberosDelegatingAuthenticationProvider implements AuthenticationProvider {

    private KerberosServiceAuthenticationProvider authnProvider;

    private final SettingsService settingsService;
    private final KerberosUserDetailsService userDetailsService;
    private final UserDetailsContextMapper userDetailsContextMapper;

    @PostConstruct
    public void configure() throws Exception {
        validateAndSetup();
        log.info("Kerberos ENABLED - authentication will be attempted through Kerberos");
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        if (authnProvider != null) {
            log.info("Kerberos delegating authenticate() call to KerberosServiceAuthenticationProvider");
            return authnProvider.authenticate(authentication);
        } else {
            log.info("Kerberos not enabled, returning null from authenticate()");
            return null;
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return KerberosServiceRequestToken.class.isAssignableFrom(authentication);
    }

    private void validateAndSetup() throws Exception {
        if (log.isDebugEnabled()) {
            logSettings();
        }
        validateMandatorySettings();
        setupMandatoryResources();
        if (isLdapUserLookupEnabled()) {
            validateOptionalSettings();
            setupOptionalResources();
        } else {
            log.warn("Kerberos: Optional settings for Kerberos LDAP user lookup not configured - unable to perform dynamic provisioning of users and refreshing of security groups");
        }
    }

    private void logSettings() {
        // check what we have is what we expect from the db
        log.debug("Kerberos settings read - START:");
        log.debug("{} {}", KerberosSettings.KEYTAB_LOCATION_KEY, getSetting(KerberosSettings.KEYTAB_LOCATION_KEY));
        log.debug("{} {}", KerberosSettings.SERVICE_PRINCIPAL_KEY, getSetting(KerberosSettings.SERVICE_PRINCIPAL_KEY));
        log.debug("{} {}", KerberosSettings.krb5_DEBUG, getSetting(KerberosSettings.krb5_DEBUG));
        log.debug("{} {}", KerberosSettings.krb5_FILE, getSetting(KerberosSettings.krb5_FILE));
        log.debug("{} {}", KerberosSettings.LDAP_USER_LOOKUP_ENABLED_KEY, isSetting(KerberosSettings.LDAP_USER_LOOKUP_ENABLED_KEY));
        log.debug("{} {}", KerberosSettings.LDAP_URL_KEY, getSetting(KerberosSettings.LDAP_URL_KEY));
        log.debug("{} {}", KerberosSettings.LDAP_USER_DN_KEY, getSetting(KerberosSettings.LDAP_USER_DN_KEY));
        log.debug("{} {}", KerberosSettings.LDAP_USER_PASS_KEY, getSetting(KerberosSettings.LDAP_USER_PASS_KEY));
        log.debug("{} {}", KerberosSettings.LDAP_SEARCH_BASE_KEY, getSetting(KerberosSettings.LDAP_SEARCH_BASE_KEY));
        log.debug("{} {}", KerberosSettings.LDAP_SEARCH_FILTER_KEY, getSetting(KerberosSettings.LDAP_SEARCH_FILTER_KEY));
        log.debug("Kerberos settings read - END:");
    }

    private void validateMandatorySettings() {
        StringBuilder errorMsgs = new StringBuilder();
        validateSetting(KerberosSettings.SERVICE_PRINCIPAL_KEY, errorMsgs);
        validateSetting(KerberosSettings.KEYTAB_LOCATION_KEY, errorMsgs);
        if (errorMsgs.length() > 0) {
            throw new IllegalArgumentException("Kerberos: Invalid settings for Kerberos authentication: \n" + errorMsgs);
        }
    }

    private void setupMandatoryResources() throws Exception {
        SunJaasKerberosTicketValidator ticketValidator = new SunJaasKerberosTicketValidator();
        UrlResource keyTab = new UrlResource(getSetting(KerberosSettings.KEYTAB_LOCATION_KEY));
        ticketValidator.setKeyTabLocation(keyTab);
        ticketValidator.setServicePrincipal(getSetting(KerberosSettings.SERVICE_PRINCIPAL_KEY));
        ticketValidator.setDebug(true);
        ticketValidator.afterPropertiesSet();
        authnProvider = new KerberosServiceAuthenticationProvider();
        authnProvider.setTicketValidator(ticketValidator);
        authnProvider.setUserDetailsService(userDetailsService);
    }

    private boolean isLdapUserLookupEnabled() {
        return isSetting(KerberosSettings.LDAP_USER_LOOKUP_ENABLED_KEY);
    }

    private void validateOptionalSettings() {
        StringBuilder errorMsgs = new StringBuilder();
        validateSetting(KerberosSettings.LDAP_URL_KEY, errorMsgs);
        validateSetting(KerberosSettings.LDAP_USER_DN_KEY, errorMsgs);
        validateSetting(KerberosSettings.LDAP_USER_PASS_KEY, errorMsgs);
        validateSetting(KerberosSettings.LDAP_SEARCH_BASE_KEY, errorMsgs);
        validateSetting(KerberosSettings.LDAP_SEARCH_FILTER_KEY, errorMsgs);
        if (errorMsgs.length() > 0) {
            throw new IllegalArgumentException("Kerberos: Invalid settings for Kerberos LDAP user lookup: \n" + errorMsgs);
        }
    }

    private void setupOptionalResources() throws Exception{
        LdapContextSource ldapContextSource = new DefaultSpringSecurityContextSource(getSetting(KerberosSettings.LDAP_URL_KEY));
        ldapContextSource.setUserDn(getSetting(KerberosSettings.LDAP_USER_DN_KEY));
        ldapContextSource.setPassword(getSetting(KerberosSettings.LDAP_USER_PASS_KEY));
        ldapContextSource.afterPropertiesSet();
        LdapUserSearch ldapUserSearch = new FilterBasedLdapUserSearch(getSetting(KerberosSettings.LDAP_SEARCH_BASE_KEY), getSetting(KerberosSettings.LDAP_SEARCH_FILTER_KEY), ldapContextSource);
        LdapUserDetailsService ldapUserDetailsService = new LdapUserDetailsService(ldapUserSearch);
        ldapUserDetailsService.setUserDetailsMapper(userDetailsContextMapper);
        userDetailsService.setLdapUserDetailsService(ldapUserDetailsService);
    }

    private boolean validateSetting(String settingKey, StringBuilder errorMsgs) {
        boolean valid = isValidSetting(settingKey);
        if (!valid) errorMsgs.append("Kerberos: Setting " + settingKey + " is missing/blank \n");
        return valid;
    }

    private boolean isValidSetting(String settingKey) {
        String setting = getSetting(settingKey);
        if (StringUtils.isBlank(setting)) {
            return false;
        } else {
            return true;
        }
    }

    private String getSetting(String key) {
        return settingsService.settingFor(SettingsService.Namespace.KERBEROS_SETTINGS_NAMESPACE, key).getValue();
    }

    private boolean isSetting(String key) {
        return settingsService.settingFor(SettingsService.Namespace.KERBEROS_SETTINGS_NAMESPACE, key).isTrue();
    }

}
