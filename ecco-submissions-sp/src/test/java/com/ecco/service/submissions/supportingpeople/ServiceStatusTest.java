package com.ecco.service.submissions.supportingpeople;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.ContextConfiguration;

@RunWith(SpringJUnit4ClassRunner.class)
//@Transactional(TransactionMode.ROLLBACK)
@ContextConfiguration(locations = {"classpath:applicationContext-service-remote.xml"})
@Ignore(value="we don't want to hammer a real web service in every build")
public class ServiceStatusTest {

    @Autowired
    private ServiceStatusService serviceStatus;

    @Before
    public void initialise() {
        //SecurityContextHolder.getContext().setAuthentication(null);
    }

    @Test
    public void test_ServiceStatus() throws Exception {
        ServiceStatusResponse answer = (ServiceStatusResponse) serviceStatus.serviceStatus();
        System.out.println(answer.getServiceStatusResult());
    }

}
