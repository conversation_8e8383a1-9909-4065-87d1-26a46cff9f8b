package com.ecco.webApi.evidence;

import com.ecco.config.service.SettingsService;
import com.ecco.dao.EvidenceFormSnapshotRepository;
import com.ecco.dao.EvidenceFormWorkRepository;
import com.ecco.dao.EvidenceFormWorkSummary;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.EvidenceFormSnapshot;
import com.ecco.dom.EvidenceFormWork;
import com.ecco.dom.commands.DeleteCommand;
import com.ecco.service.TaskDefinitionService;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.controllers.NoContentException;
import com.ecco.webApi.controllers.NotFoundException;
import com.ecco.webApi.viewModels.Result;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.querydsl.QPageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import static com.ecco.config.service.SettingsService.Evidence.PageSizeHistory;
import static com.ecco.security.SecurityUtil.getUser;
import static org.springframework.http.HttpStatus.NO_CONTENT;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * This is generally used with Json Patch for form data, but could potentially be used for retrieving snapshots of
 * things that are built from an event source (e.g. commands), and may have periodic snapshots.
 */
@RestController
public class EvidenceFormWorkController extends BaseWebApiController {

    private final EvidenceFormSnapshotToViewModel snapshotToViewModel;
    private final EvidenceFormWorkSummaryToViewModel summaryToViewModel = new EvidenceFormWorkSummaryToViewModel();
    private final EvidenceFormWorkToViewModel workToViewModel = new EvidenceFormWorkToViewModel();

    private final EvidenceFormSnapshotCommandHandler evidenceFormSnapshotCommandHandler;
    private final ServiceRecipientCommandRepository commandRepository;
    private final EvidenceFormWorkRepository evidenceFormWorkRepository;
    private final EvidenceFormSnapshotRepository evidenceFormSnapshotRepository;
    private final SettingsService settingsService;
    private final TaskDefinitionService taskDefinitionService;

    public EvidenceFormWorkController(EvidenceFormSnapshotCommandHandler evidenceFormSnapshotCommandHandler, ServiceRecipientCommandRepository commandRepository, EvidenceFormWorkRepository evidenceFormWorkRepository, EvidenceFormSnapshotRepository evidenceFormSnapshotRepository, SettingsService settingsService, TaskDefinitionService taskDefinitionService) {
        this.evidenceFormSnapshotCommandHandler = evidenceFormSnapshotCommandHandler;
        this.commandRepository = commandRepository;
        this.evidenceFormWorkRepository = evidenceFormWorkRepository;
        this.evidenceFormSnapshotRepository = evidenceFormSnapshotRepository;
        this.settingsService = settingsService;
        this.taskDefinitionService = taskDefinitionService;
        this.snapshotToViewModel =  new EvidenceFormSnapshotToViewModel(taskDefinitionService);
    }

    @PostJson(value = {"/service-recipients/{serviceRecipientId}/evidence/json/{evidenceGroupKey}/{taskName}/",
                        "/service-recipients/{serviceRecipientId}/evidence/form/{evidenceGroupKey}/{taskName}/"})
    public Result patchSnapshot(@Nonnull Authentication authentication, @Nonnull EvidenceParams params,
                                @Nonnull @RequestBody String requestBody) throws IOException {
        return evidenceFormSnapshotCommandHandler.handleCommand(authentication, params, requestBody);
    }

    /**
     * Gets the latest snapshot (custom form entry).
     */
    @GetJson("/service-recipients/{serviceRecipientId}/evidence/form/{evidenceGroupKey}/snapshots/latest/")
    public EvidenceFormWorkViewModel findLatestByServiceRecipientIdAndEvidenceGroupKey(@PathVariable int serviceRecipientId,
                                                                                       @Nonnull @PathVariable String evidenceGroupKey,
                                                                                       WebRequest request) {

        if (isUnmodifiedAndSetLastModified(serviceRecipientId, request)) { // TODO: Confirm that this works
            return null;
        }

        final List<EvidenceFormSnapshot> snapshots = evidenceFormSnapshotRepository.findLatestSnapshotByCreatedAndServiceRecipientIdAndEvidenceTaskGroupKey(
                serviceRecipientId, evidenceGroupKey, new DateTime(DateTimeZone.UTC), PageRequest.of(0, 1));
        if (snapshots.isEmpty()) {
            throw new NoContentException(serviceRecipientId + "-" + evidenceGroupKey);
        }
        return snapshotToViewModel.apply(snapshots.get(0));
    }

    /** Return latest form evidence across all evidence groups for this serviceRecipientId */
    @GetJson("/service-recipients/{serviceRecipientId}/evidence/form/snapshots/latest/")
    public Stream<EvidenceFormWorkViewModel> findLatestByServiceRecipientId(
            @PathVariable int serviceRecipientId,
            WebRequest request
    ) {
        if (isUnmodifiedAndSetLastModified(serviceRecipientId, request)) { // TODO: Confirm that this works
            return null;
        }

        final List<EvidenceFormSnapshot> snapshots = evidenceFormSnapshotRepository.findLatestSnapshotsPerEvidenceGroupByServiceRecipientId(serviceRecipientId);
        if (snapshots.isEmpty()) {
            ((ServletWebRequest) request).getResponse().setStatus(NO_CONTENT.value());
            return Stream.empty();
        }
        return snapshots.stream().map(snapshotToViewModel);
    }

    /**
     * Gets the custom form history - used for attachments.
     * NB evidence-form over evidence/form/ to avoid conflict with SupportEvidenceController not using evidence/needs/
     */
    @GetJson("/service-recipients/{serviceRecipientId}/evidence-form/{evidenceGroupKey}/attachments/")
    public Slice<EvidenceFormWorkViewModel> findAttachmentsByServiceRecipientId(@PathVariable int serviceRecipientId,
                                                                          @PathVariable(required = false) String evidenceGroupKey,
                                                                          @RequestParam(name = "page", required = false) Integer page,
                                                                          WebRequest request) {
        return findByServiceRecipientId(serviceRecipientId, evidenceGroupKey, page, true, null, null, request);
    }

    /**
     * Gets the snapshot (custom form entry) at a specified work id, or the nearest one before it.
     * This is useful when we want to know the latest snapshot from the workUuid provided, which is used
     * by printing from one work item in the history to the signed snapshot.
     */
    @GetJson("/service-recipients/{serviceRecipientId}/evidence-form/{evidenceGroupKey}/{workUuid}/")
    public EvidenceFormWorkViewModel findLatestByServiceRecipientIdAndEvidenceGroupKey(@PathVariable int serviceRecipientId,
                                                                                       @Nonnull @PathVariable String evidenceGroupKey,
                                                                                       @Nonnull @PathVariable UUID workUuid,
                                                                                       @RequestParam(value = "findLastSignedSnapshot", defaultValue = "false") boolean findLastSignedSnapshot,
                                                                                       WebRequest request) {

        if (isUnmodifiedAndSetLastModified(serviceRecipientId, request)) { // TODO: Confirm that this works
            return null;
        }

        EvidenceFormWork existingWork = evidenceFormWorkRepository.findById(workUuid).orElseThrow();

        // check if we requested the 'last signed' and it already is, or else get the 'last signed' from this point if its not
        if (Boolean.TRUE.equals(findLastSignedSnapshot) && (existingWork.getSignature() == null || existingWork.getSnapshot() == null)) {
            DateTime created = existingWork.getCreated(); // created is the only thing that is reliable we can use
            List<EvidenceFormSnapshot> existingSigned = evidenceFormSnapshotRepository.findLatestSignedSnapshotByCreatedAndServiceRecipientIdAndEvidenceTaskGroupKey(
                    serviceRecipientId, evidenceGroupKey, created, PageRequest.of(0, 1));
            existingWork = existingSigned.size() > 0 ? existingSigned.get(0).getWork() : null;
        }

        if (existingWork == null) {
            throw new NotFoundException(serviceRecipientId + "-" + evidenceGroupKey + ": " + workUuid);
        }
        return workToViewModel.apply(existingWork);
    }

    /**
     * Gets the custom form history ordered by newest first: workDate DESC, created DESC - used for history.
     * NB evidence-form over evidence/form/ to avoid conflict with SupportEvidenceController not using evidence/needs/
     */
    @GetJson(value = {"/service-recipients/{serviceRecipientId}/evidence-form/{evidenceGroupKey}/",
                      "/service-recipients/{serviceRecipientId}/evidence-form/"})
    public Slice<EvidenceFormWorkViewModel> findByServiceRecipientId(@PathVariable int serviceRecipientId,
                                                                         @PathVariable(required = false) String evidenceGroupKey,
                                                                         @RequestParam(name = "page", required = false) Integer page,
                                                                         @RequestParam(value = "fromDateTime", required = false) @DateTimeFormat(iso= DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromDateTime,
                                                                         @RequestParam(value = "toDateTime", required = false) @DateTimeFormat(iso= DateTimeFormat.ISO.DATE_TIME) LocalDateTime toDateTime,
                                                                         WebRequest request) {
        return findByServiceRecipientId(serviceRecipientId, evidenceGroupKey, page, false, fromDateTime, toDateTime, request);
    }

    public Slice<EvidenceFormWorkViewModel> findByServiceRecipientId(int serviceRecipientId, @Nullable String evidenceGroupKey, @Nullable Integer page, boolean findAttachmentsOnly, @Nullable LocalDateTime from, @Nullable LocalDateTime to, WebRequest request) {
        if (isUnmodifiedAndSetLastModified(serviceRecipientId, request)) {
            return null;
        }

        QPageRequest pr = page == null ? null : settingsService.settingFor(PageSizeHistory).asQPageRequest(page);
        var evidenceGroup = taskDefinitionService.findGroupFromGroupName(evidenceGroupKey);
        Assert.state(from == null || to == null || evidenceGroup != null, "from/to dates without evidenceGroup is unimplemented");
        final Slice<EvidenceFormWorkSummary> formWork =
                evidenceFormWorkRepository.findAllEvidenceFormWork(serviceRecipientId, evidenceGroup, pr, findAttachmentsOnly, from, to);
        return formWork.map(summaryToViewModel);
    }

    /**
     * Delete the evidence using the sysadmin-like admin screen.
     * TODO: Change to proper command based with command sent from client and able to be done offline
     * TODO: In body record workUuid, workDate of evidence that got deleted
     */
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @RequestMapping(value = "/evidence/form/{workUuid}", method = RequestMethod.DELETE, consumes = APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.ACCEPTED)
    public Result delete(
            @PathVariable UUID workUuid,
            @RequestBody EvidenceFormWorkViewModel evidence,
            @Nonnull Authentication authentication) {
        Assert.isNull(evidence.id, "No id should be set in request body on DELETE");

        EvidenceFormWork existing = evidenceFormWorkRepository.findOne(workUuid);

        Assert.state(existing.getServiceRecipientId().equals(evidence.serviceRecipientId),
                "The supplied serviceRecipientId did not match");

        evidenceFormWorkRepository.delete(existing);

        long userId = getUser(authentication).getId();
        DeleteCommand deleteCommand = new DeleteCommand(UUID.randomUUID(), DateTime.now().toInstant(),
                userId, "{}", // For effin Oracle
                evidence.serviceRecipientId, "form", null);
        commandRepository.save(deleteCommand);
        return new Result("deleted");
    }

}
