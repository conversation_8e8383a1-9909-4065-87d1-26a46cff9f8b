package com.ecco.offline;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.security.MessageDigest;
import java.util.*;

import com.ecco.web.EccoMediaTypes;

public class CacheManifestBuilder {
    private boolean preferOnline;
    private final MessageDigest digest;
    private final List<URI> cachedResourceUris;
    private final List<URI> networkResourceUris;
    private final List<Map.Entry<URI, URI>> fallbackResourceUris;

    public CacheManifestBuilder() {
        digest = DigestUtils.getSha256Digest();
        cachedResourceUris = new ArrayList<URI>();
        networkResourceUris = new ArrayList<URI>();
        fallbackResourceUris = new ArrayList<Map.Entry<URI, URI>>();
    }

    public CacheManifestBuilder setPreferOnline(boolean preferOnline) {
        this.preferOnline = preferOnline;
        return this;
    }

    public CacheManifestBuilder addCachedResource(String relativePath, ResponseEntity<byte[]> responseEntity)
            throws URISyntaxException {
        digestResource("C", null, relativePath, responseEntity);
        cachedResourceUris.add(new URI(null, null, relativePath, null));
        return this;
    }

    public CacheManifestBuilder addNetworkResource(String relativePath) throws URISyntaxException {
        digestResource("N", null, relativePath, null);
        networkResourceUris.add(new URI(null, null, relativePath, null));
        return this;
    }

    public CacheManifestBuilder addFallbackResource(String namespace, String relativePath, ResponseEntity<byte[]> responseEntity)
            throws URISyntaxException {
        digestResource("F", namespace, relativePath, responseEntity);
        fallbackResourceUris.add(new AbstractMap.SimpleEntry<URI, URI>(
                new URI(null, null, namespace, null),
                new URI(null, null, relativePath, null)));
        return this;
    }

    public ResponseEntity<byte[]> build() {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = null;

        String digestBase64 = Base64.getEncoder().encodeToString(this.digest.digest());

        try {
            try {
                writer = new OutputStreamWriter(byteArrayOutputStream, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                // This should never happen since UTF-8 is a built-in encoding.
                throw new Error(e);
            }

            try {
                writer.write("CACHE MANIFEST\n#");
                writer.write(digestBase64);
                writer.write("\n");

                for (URI uri : cachedResourceUris) {
                    writer.write(uriToString(uri));
                    writer.write("\n");
                }


                if (networkResourceUris.size() > 0) {
                    writer.write("NETWORK:\n");
                    for (URI uri : networkResourceUris) {
                        writer.write(uriToString(uri));
                        writer.write("\n");
                    }
                }

                if (fallbackResourceUris.size() > 0) {
                    writer.write("FALLBACK:\n");
                    for (Map.Entry<URI, URI> uriPair : fallbackResourceUris) {
                        writer.write(uriToString(uriPair.getKey()));
                        writer.write(" ");
                        writer.write(uriToString(uriPair.getValue()));
                        writer.write("\n");
                    }
                }

                if (preferOnline) {
                    writer.write("SETTINGS:\nprefer-online\n");
                }

                writer.close();
            } catch (IOException e) {
                // This should never happen since we are writing to a byte array.
                throw new Error(e);
            }
        } finally {
            IOUtils.closeQuietly(writer);
            IOUtils.closeQuietly(byteArrayOutputStream);
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(EccoMediaTypes.TEXT_CACHE_MANIFEST);
        headers.setETag("W/\"" + digestBase64 + "\"");
        headers.setLastModified(System.currentTimeMillis());
        headers.setExpires(new DateTime().plusSeconds(10).getMillis());


        return new ResponseEntity<byte[]>(byteArrayOutputStream.toByteArray(), headers, HttpStatus.OK);
    }

    private void digestResource(String type, String namespace, String relativePath, ResponseEntity<byte[]> responseEntity) {
        if (namespace == null) {
            namespace = "";
        }

        if (responseEntity == null) {
            responseEntity = new ResponseEntity<byte[]>(new byte[0], HttpStatus.OK);
        }

        digestIdentifiers(type, namespace, relativePath);

        digestHeaders(responseEntity.getHeaders());

        digestBody(responseEntity.getBody());
    }

    private void digestIdentifiers(String type, String namespace, String relativePath) {
        DigestUtils.updateDigest(digest, type);
        DigestUtils.updateDigest(digest, ":");
        DigestUtils.updateDigest(digest, Integer.toString(namespace.length()));
        DigestUtils.updateDigest(digest, namespace);
        DigestUtils.updateDigest(digest, ":");
        DigestUtils.updateDigest(digest, Integer.toString(relativePath.length()));
        DigestUtils.updateDigest(digest, ":");
        DigestUtils.updateDigest(digest, relativePath);
        DigestUtils.updateDigest(digest, ":");
    }

    private void digestHeaders(HttpHeaders headers) {
        DigestUtils.updateDigest(digest, Integer.toString(headers.size()));
        DigestUtils.updateDigest(digest, ":");

        for (String headerName : new TreeSet<String>(headers.keySet())) {
            if (includeHeaderInDigest(headerName)) {
                for (String headerValue : headers.get(headerName)) {
                    DigestUtils.updateDigest(digest, Integer.toString(headerName.length()));
                    DigestUtils.updateDigest(digest, ":");
                    DigestUtils.updateDigest(digest, headerName);
                    DigestUtils.updateDigest(digest, ":");
                    DigestUtils.updateDigest(digest, Integer.toString(headerValue.length()));
                    DigestUtils.updateDigest(digest, ":");
                    DigestUtils.updateDigest(digest, headerValue);
                    DigestUtils.updateDigest(digest, ":");
                }
            }
        }
    }

    private void digestBody(byte[] body) {
        DigestUtils.updateDigest(digest, Long.toString(body.length));
        DigestUtils.updateDigest(digest, ":");
        DigestUtils.updateDigest(digest, body);
        DigestUtils.updateDigest(digest, ":");
    }

    /**
     * Converts a URI to a URI string in the format required for use in a cache manifest.
     */
    private static String uriToString(URI uri) {
        String s = uri.toASCIIString();

        if (s.length() == 0) {
            return ".";
        } else {
            return s;
        }
    }

    /**
     * Tests if a given HTTP header should be included in the Cache Manifest
     * digest.
     */
    private static boolean includeHeaderInDigest(String headerName) {
        return !("Cache-Control".equals(headerName))
                && !("ETag".equals(headerName))
                && !("Last-Modified".equals(headerName));
    }
}
