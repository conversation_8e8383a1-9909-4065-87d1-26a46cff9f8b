import $ = require("jquery");
import {AuditHistoryIntegrations, AuditHistoryProps} from "ecco-components";
import {BaseServiceRecipientCommandDto, SessionData} from "ecco-dto";
import * as React from "react";
import * as ReactDom from "react-dom";
import CommandHistoryListControl from "../CommandHistoryListControl";
import {CommandHistoryItemControl} from "../CommandHistoryItemControl";

/** AuditHistory integrations we can pass in via EccoAPI */
export const auditHistoryIntegrations: AuditHistoryIntegrations = {
    handleCommand: (command: BaseServiceRecipientCommandDto, sessionData: SessionData) => {
        return CommandHistoryItemControl.createCommandViewHandler(command, sessionData);
    },
    componentFactory: (sessionData: SessionData) => {
        // NB this integration is only called from AuditHistoryPaged currently - which is SR audits
        // manager and above can see the detail on SR audits
        const detailPermission = sessionData.hasTopLevelGroup('manager', true);
        return new CommandHistoryItemControl(sessionData, detailPermission);
    }
}

// NB DEFAULT export
export class AuditHistory extends React.Component<AuditHistoryProps> {
    private control: CommandHistoryListControl;

    constructor(props: AuditHistoryProps) {
        super(props);

        const manager = props.sessionData.hasTopLevelGroup("manager", true);
        this.control = props.resource
                ? // un-paged: SettingAppBar config, UserView - can see detail on audits
                CommandHistoryListControl.createFromHateoasResource(props.resource, true)
                : props.uuid
                        ? // ActivityView
                        CommandHistoryListControl.createWithOneId(props.uuid, manager)
                        : // TODO replace with AuditHistoryPaged to get paging for these areas (also need paging for props.resource)
                          // StaffView, ContractView, offline/router (new and ond client file), BuildingView, ServiceRecipientView, RotaWorkerView, ShiftView
                        CommandHistoryListControl.createWithIds(
                                props.serviceRecipientId,
                                props.evidenceGroup,
                                props.taskName,
                                manager
                        );
    }

    public componentDidMount() {
        $(ReactDom.findDOMNode(this) as Element).append(this.control.element());
        this.control.load();
    }

    render() {
        return <div />;
    }
}
