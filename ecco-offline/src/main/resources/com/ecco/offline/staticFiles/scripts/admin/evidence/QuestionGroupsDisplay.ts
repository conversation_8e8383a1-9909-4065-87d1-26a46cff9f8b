import _ = require("lodash");

import * as dto from "ecco-dto/service-config-dto";

// This class is responsible for displaying the question
class QuestionGroupsDisplay {

    public render(questionGroups: dto.QuestionGroup[]): string {
        var reduceType = (html: string, qaf: dto.QuestionAnswerFree) => {
                html += _.escape(qaf.valueType) + "<br>";
                return html;
            };

            var reduceChoice = (html: string, choice: dto.QuestionAnswerChoice) => {
                html += _.escape(choice.displayValue) + ":" +_.escape(choice.value) +";";
                return html;
            };

        var reduceQuestion = (html: string, question: dto.Question) => {
                html += _.escape(question.name) + "<br>";
                html += question.freeTypes.reduce(reduceType, "");
                html += question.choices.reduce(reduceChoice, "");
                html += "<br><br>";
                return html;
            };

        var reduceQuestionGroup = (html: string, questionGroup: dto.QuestionGroup) => {
            if (questionGroup.id) {
                html += "---- questionGroup: " + questionGroup.id.toString() + " ----<br>" + _.escape(questionGroup.name) + "<br>";
            }
            html += questionGroup.questions.reduce(reduceQuestion, "");
            return html;
        };

        return questionGroups.reduce(reduceQuestionGroup , "");
    }

}

export = QuestionGroupsDisplay;
