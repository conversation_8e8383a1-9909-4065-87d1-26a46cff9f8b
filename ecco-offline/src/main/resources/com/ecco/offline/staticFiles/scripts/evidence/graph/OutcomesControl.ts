import $ = require("jquery");

import BaseEvidenceControl = require("../BaseEvidenceControl");
import dynamicTree = require("../../draw/dynamic-tree");
import GraphContext = require("./GraphContext");
import ReferralNode = require("./ReferralNode");
import services = require("ecco-offline-data");
import DynamicTreeControl = dynamicTree.DynamicTreeControl;
import SupportAction = evidenceDto.SupportAction;
import SessionData = featuresDomain.SessionData;
import * as domain from "ecco-dto";
import * as featuresDomain from "ecco-dto";
import {SmartStepStatus} from "ecco-dto";
import * as evidenceDto from "ecco-dto/evidence-dto";
import {GoalUpdateCommandDto} from "ecco-dto/evidence-dto";
import {EvidenceDef, SmartStepStatusTransitions, SupportSmartStepsSnapshot} from "ecco-evidence";
import {EvidenceControl} from "../evidenceControls";
import {CommandQueue, GoalUpdateCommand} from "ecco-commands";
import {EccoDate, ResizeEvent} from "@eccosolutions/ecco-common";

/**
 * Visual representation of a container with multiple SmartSteps (Actions). This can match the content of a
 * tab on a needs assessment/support plan/review.
 */
class OutcomesControl extends BaseEvidenceControl implements EvidenceControl {

    private treeControl = new DynamicTreeControl(560,560);
    private rootNode: ReferralNode;
    private $container = $("<div>")
        .append(this.treeControl.element());


    private smartStepStatusTransitions: SmartStepStatusTransitions;


    // stubbed - changes are not made from here
    // TODO remove this class from BaseEvidenceForm
    public emitChangesTo(queue: CommandQueue) {
    }

    constructor(private serviceRecipientId: number, private serviceType: domain.ServiceType,
        private evidenceDef: EvidenceDef, private features: SessionData) {
        super();

        const snapshot = services.getSupportSmartStepsSnapshotRepository()
            .findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroup(serviceRecipientId,
                evidenceDef.getEvidenceGroup().name);
        // NOTE: Assumes Referral for graph control
        const referral = services.getReferralRepository().findOneServiceRecipientWithEntities(serviceRecipientId);

        Promise.all([snapshot, referral])
            .then(([snapshot, recipient]) => {
                this.treeControl.element().appendTo(this.$container);

                this.smartStepStatusTransitions = new SmartStepStatusTransitions(evidenceDef.getEvidencePageType(),
                        recipient.configResolver.getServiceType().getTaskDefinitionSetting(
                                            evidenceDef.getTaskName(),
                                             "allowActionStraightToAchieve") == "y");

                this.subscribeToEvents();

                const context = new GraphContext(this.serviceRecipientId, recipient, [], this.treeControl,
                    evidenceDef, this.smartStepStatusTransitions, features, recipient.configResolver);
                this.rootNode = new ReferralNode(recipient, context);
                this.treeControl.setContextNode(this.rootNode.getNode());
                this.createSupportInstanceControls(snapshot);
                ResizeEvent.bus.fire(new ResizeEvent());
            });
    }

    /** after initial data has been gathered, prepare the control */
    private createSupportInstanceControls(snapshot: evidenceDto.SupportSmartStepsSnapshotDto) {
        const es = new SupportSmartStepsSnapshot(this.serviceType, snapshot);

//        TODO: Turn this into using an EvidenceSnapshot

// was        var actions = this.smartStepEvidenceDeltaGenerator.getLatestSupportActions();
        this.createControlsFromInitialActions(snapshot.latestActions);
    }

    private createControlsFromInitialActions(supportActions: SupportAction[]) {
        supportActions.forEach( (supportAction) => {
            const action = this.serviceType.getActionById(supportAction.actionId);
            if (action) {
                const added = this.rootNode.addAction(action, supportAction);
            }
        });
    }

    // via an event from subscribeToEvents above
    // @Override
    public updateAction(goalUpdateCommand: GoalUpdateCommand): void {
        // TODO provide the new SupportAction in the method call
        // this ends up generating a SupportAction based on what is changed
        let dto = <GoalUpdateCommandDto> goalUpdateCommand.toCommandDto();
        let actionDefId = dto.actionDefId!;
        const action = this.serviceType.getActionById(actionDefId);
        if (action) {
            const supportAction: SupportAction = {
                id: null, // id is the database numeric id
                actionInstanceUuid: dto.actionInstanceUuid || null,
                parentActionInstanceUuid: dto.parentActionInstanceUuid || null,
                workId: dto.workUuid,
                workDate: null!, // Not available in this context
                actionId: actionDefId,
                name: action.getName(),
                goalName: dto.goalNameChange ? dto.goalNameChange.to : null,
                goalPlan: dto.goalPlanChange ? dto.goalPlanChange.to : null,
                targetDateTime: dto.targetDateChange && dto.targetDateChange.to ? EccoDate.parseIso8601(dto.targetDateChange.to).toDateTimeMidnight().formatIso8601() : null,
                expiryDate: dto.expiryDateChange ? dto.expiryDateChange.to : null,
                status: SmartStepStatus.WantToAchieve,
                statusChange: true, // can't tell if there is a status change
                score: dto.scoreChange ? dto.scoreChange.to : null,
                statusChangeReasonId: null,
                hierarchy: dto.hierarchyChange ? dto.hierarchyChange.to : null,
                position: dto.positionChange ? dto.positionChange.to : null
            };
            this.rootNode.addAction(action, supportAction);
        }
    }

    public updateQuestionAnswer(questionDefId: number, answer: string): void {
    }

    public element(): $.JQuery {
        return this.$container;
    }
}
export = OutcomesControl;
