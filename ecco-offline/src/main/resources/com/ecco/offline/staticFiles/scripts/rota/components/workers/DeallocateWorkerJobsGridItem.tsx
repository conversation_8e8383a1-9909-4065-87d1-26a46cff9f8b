import {ReactElement} from "react";
import {DemandResource} from "ecco-rota";
import {DeallocateWorkerJobButton} from "./DeallocateWorkerJobButton";
import * as React from "react";
import {Grid} from "@eccosolutions/ecco-mui";

export interface Props {
    readonly allocatedWorkerJobs: readonly DemandResource[];
    readonly onDeallocate: (workerJob: DemandResource) => void;
}

export function DeallocateWorkerJobsGridItem({allocatedWorkerJobs, onDeallocate}: Props): ReactElement {
    return allocatedWorkerJobs.length === 0
            ? null
            : <Grid item>{
                allocatedWorkerJobs.map((workerJob, i) => <span key={i}>
                    <DeallocateWorkerJobButton workerJob={workerJob} onClick={() => onDeallocate(workerJob)}/>
                </span>)
            }</Grid>;
}