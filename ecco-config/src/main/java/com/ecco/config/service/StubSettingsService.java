package com.ecco.config.service;

import java.util.List;

import javax.annotation.Nonnull;

import com.ecco.config.dom.Setting;

/**
 * Stub for use in integration tests
 */
public class StubSettingsService implements SettingsService {
    @Override
    public void setSetting(String namespace, String key, String value) {
    }

    @Override
    public Setting settingFor(String namespace, String key) {
        return null;
    }

    @Nonnull
    @Override
    public Setting settingFor(SettingKey key) {
        return null;
    }

}